# 🏆 Walkthrough: Ejercicios ICFES Juegos Deportivos - Interpretación y Representación

## 📋 Descripción General del Proyecto

Este proyecto contiene **dos versiones complementarias** de ejercicios ICFES de matemáticas sobre **análisis de datos deportivos**, ambos enfocados en la competencia de **Interpretación y Representación** con nivel de dificultad 2.

### 📁 Archivos del Proyecto
- `juegos_deportivos_aleatorio_interpretacion_representacion_n2_opcA_v1.Rmd` - **Versión A**
- `juegos_deportivos_aleatorio_interpretacion_representacion_n2_opcB_v1.Rmd` - **Versión B**

## 🎯 Competencia ICFES Evaluada

**Competencia:** Interpretación y Representación  
**Nivel:** 2 (Intermedio)  
**Componente:** Aleatorio (Estadística)  
**Contexto:** Comunitario  
**Eje Axial:** Eje 4

## 🔄 Diferencias Clave Entre Versiones

### 🅰️ **VERSIÓN A (opcA)** - Identificación de Errores
- **Enfoque:** Evalúa la capacidad de **identificar errores** en procedimientos matemáticos
- **Respuesta Correcta:** "incorrecto, porque se debe multiplicar [decimal_correcto] por el número de atletas"
- **Procedimiento Presentado:** Usa valor decimal **INCORRECTO** (ej: 0.7 en lugar de 0.07 para 7%)
- **Competencia Específica:** Detectar errores conceptuales en conversión porcentaje-decimal

### 🅱️ **VERSIÓN B (opcB)** - Validación de Procedimientos
- **Enfoque:** Evalúa la capacidad de **validar procedimientos correctos**
- **Respuesta Correcta:** "suficiente para determinar el número de atletas que participó en [disciplina]"
- **Procedimiento Presentado:** Usa valor decimal **CORRECTO** (ej: 0.07 para 7%)
- **Competencia Específica:** Reconocer cuando un procedimiento es matemáticamente válido

## 🏗️ Arquitectura Técnica Común

### 📊 Generación de Datos Aleatorios
```r
# Contextos deportivos (10 opciones)
- Juegos Panamericanos, Olímpicos, Mundiales, etc.
- Regiones: América, mundial, Centroamérica, etc.

# Datos temporales
- 5 años consecutivos (cada 4 años, formato realista)
- Rango: 1998-2024

# Datos deportivos por año
- Países: 30-60 participantes
- Deportes: 30-50 disciplinas  
- Atletas: 3,000-9,000 participantes
```

### 🎲 Sistema Avanzado de Distractores
- **8+ tipos diferentes** de distractores conceptuales
- **30% probabilidad** de valores duplicados con justificaciones diferentes
- **Selección estratégica:** 1 duplicado + 2 diferentes (cuando aplica)
- **Verificación única:** Garantiza 4 opciones textualmente distintas

### 🐍 Integración R-Python
```python
# Generación de tablas con matplotlib
- Formato español (coma como separador de miles)
- Estilización profesional con colores
- Resaltado del año de cálculo
- Compatibilidad PDF/HTML/Word
```

## 📈 Visualizaciones y Tablas

### 🎨 Características Visuales
- **Encabezados:** Verde (#4CAF50) con texto blanco
- **Filas alternas:** Gris claro (#f0f0f0) y blanco
- **Año destacado:** Amarillo (#FFE082) con texto en negrita
- **Resolución:** 150 DPI para calidad profesional

### 📱 Adaptabilidad de Formato
```r
# Detección automática de formato
if (es_moodle) {
  # Tabla HTML responsiva
} else {
  # Imagen PNG/PDF generada con Python
}
```

## 🧮 Lógica Matemática

### ✅ **Versión A - Detección de Errores**
```
Porcentaje real: 7%
Decimal correcto: 0.07
Decimal presentado: 0.7 (ERROR)
Resultado incorrecto: 0.7 × atletas = 70% (no 7%)
```

### ✅ **Versión B - Validación Correcta**
```
Porcentaje real: 7%  
Decimal presentado: 0.07 (CORRECTO)
Resultado correcto: 0.07 × atletas = 7%
```

## 🔬 Sistema de Validaciones

### 🧪 Tests Automáticos
```r
# Rangos realistas de datos
test_that("Los datos están en rangos realistas", {
  expect_true(países >= 30 && países <= 60)
  expect_true(deportes >= 30 && deportes <= 50)  
  expect_true(atletas >= 3000 && atletas <= 9000)
})

# Coherencia matemática
test_that("El porcentaje está en rango correcto", {
  expect_true(porcentaje >= 5 && porcentaje <= 10)
  expect_true(porcentaje_decimal == porcentaje / 100)
})

# Unicidad de opciones
test_that("Las opciones son válidas", {
  expect_equal(length(unique(opciones)), 4)
  expect_true(afirmacion_correcta %in% opciones)
})
```

## 🎯 Estrategias Pedagógicas

### 📚 **Versión A - Errores Conceptuales Comunes**
- Confusión entre 0.07 y 0.7
- Malentendido en conversión porcentaje-decimal
- Identificación de procedimientos incorrectos

### 📚 **Versión B - Validación de Conocimientos**
- Reconocimiento de procedimientos válidos
- Suficiencia de información disponible
- Aplicación correcta de conceptos porcentuales

## 🚀 Compilación y Uso

### 📋 Requisitos Previos
```r
# Librerías R necesarias
library(exams)
library(reticulate) 
library(digest)
library(testthat)
library(knitr)
library(stringr)

# Python configurado correctamente
use_python(Sys.which("python"), required = TRUE)
```

### 🔧 Comandos de Compilación
```r
# Compilar versión individual
exams2pdf("juegos_deportivos_aleatorio_interpretacion_representacion_n2_opcA_v1.Rmd")
exams2pdf("juegos_deportivos_aleatorio_interpretacion_representacion_n2_opcB_v1.Rmd")

# Compilar ambas versiones
exams2pdf(c("opcA_v1.Rmd", "opcB_v1.Rmd"))

# Para Moodle
exams2moodle(c("opcA_v1.Rmd", "opcB_v1.Rmd"))
```

## 📊 Métricas de Calidad

### ✨ Diversidad de Versiones
- **Objetivo:** 300+ versiones únicas por archivo
- **Logrado:** Sistema de aleatorización avanzado
- **Verificación:** Tests automáticos incluidos

### 🎯 Alineación ICFES
- **Competencia:** Interpretación y Representación ✅
- **Nivel 2:** Dificultad intermedia apropiada ✅
- **Contexto:** Comunitario/deportivo realista ✅
- **Componente:** Aleatorio/estadístico ✅

## 🔄 Flujo de Trabajo Recomendado

1. **Selección de Versión:** Elegir A (errores) o B (validación) según objetivo pedagógico
2. **Compilación:** Usar comandos apropiados según formato destino
3. **Validación:** Verificar que tests automáticos pasen
4. **Implementación:** Desplegar en plataforma educativa
5. **Análisis:** Revisar resultados estudiantiles para ajustes futuros

## 📝 Notas Técnicas Importantes

### ⚠️ Configuraciones Críticas
- **Semilla aleatoria:** `set.seed(sample(1:100000, 1))` para máxima diversidad
- **Formato español:** Coma como separador de miles, punto decimal
- **Python/matplotlib:** Configuración `matplotlib.use('Agg')` para compatibilidad
- **LaTeX:** Paquetes tikz, xcolor, graphicx incluidos

### 🔧 Solución de Problemas Comunes
- **Error Python:** Verificar `use_python()` configurado correctamente
- **Tablas no aparecen:** Comprobar detección de formato Moodle vs PDF
- **Tests fallan:** Revisar rangos de datos aleatorios
- **Compilación LaTeX:** Verificar paquetes extra_dependencies

---

**🎓 Proyecto desarrollado siguiendo estándares ICFES y mejores prácticas R-exams**  
**📅 Versión: Junio 2025 | 🔄 Actualización continua**
